# 运行时配置部署说明

## 概述

本方案实现了在Docker容器运行时动态指定环境变量配置，而不是在构建时固化配置。

## 工作原理

1. **构建时**：代码支持运行时配置，但不包含具体配置值
2. **运行时**：容器启动时根据环境变量生成`config.js`文件
3. **前端**：优先读取运行时配置，降级到构建时配置

## 部署方式

### 方式一：使用Docker Compose（推荐）

```bash
# 1. 构建镜像
docker-compose -f docker-compose.runtime.yml build

# 2. 启动容器
docker-compose -f docker-compose.runtime.yml up -d

# 3. 查看日志
docker-compose -f docker-compose.runtime.yml logs -f
```

### 方式二：直接使用Docker命令

```bash
# 1. 构建镜像
docker build -f Dockerfile.runtime -t yyhis-web:runtime .

# 2. 运行容器
docker run -d \
  --name yyhis-web-runtime \
  -p 80:80 \
  -e VITE_YINGCHUNHUA_SDK_URL="http://183.242.68.188:32103/client_app_iframe/index.js" \
  -e VITE_YINGCHUNHUA_APP_KEY="ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09" \
  -e VITE_YINGCHUNHUA_APP_SECRET_KEY="YYCloud1644286723584" \
  yyhis-web:runtime
```

### 方式三：使用环境变量文件

```bash
# 1. 创建环境变量文件
cat > .env.runtime << EOF
VITE_YINGCHUNHUA_SDK_URL=http://183.242.68.188:32103/client_app_iframe/index.js
VITE_YINGCHUNHUA_APP_KEY=ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09
VITE_YINGCHUNHUA_APP_SECRET_KEY=YYCloud1644286723584
EOF

# 2. 运行容器
docker run -d \
  --name yyhis-web-runtime \
  -p 80:80 \
  --env-file .env.runtime \
  yyhis-web:runtime
```

## 服务器部署步骤

基于您的服务器环境，建议按以下步骤操作：

```bash
# 1. 在服务器上创建新的Dockerfile
cp Dockerfile Dockerfile.backup
cp Dockerfile.runtime Dockerfile

# 2. 确保配置生成脚本可执行
chmod +x scripts/generate-runtime-config.sh

# 3. 构建新镜像
docker build -t yyhis-web:runtime .

# 4. 停止旧容器（如果有）
docker stop yyhis-web 2>/dev/null || true
docker rm yyhis-web 2>/dev/null || true

# 5. 使用环境变量文件启动新容器
docker run -d \
  --name yyhis-web \
  -p 80:80 \
  --env-file env \
  yyhis-web:runtime

# 6. 验证部署
docker logs yyhis-web
curl http://localhost/config.js
```

## 配置验证

### 1. 检查容器日志
```bash
docker logs yyhis-web
```

应该看到类似输出：
```
🔧 生成运行时配置...
✅ 运行时配置生成完成: /usr/share/nginx/html/config.js
🚀 启动医院管理系统容器...
🌐 启动Nginx服务器...
```

### 2. 检查配置文件
```bash
# 查看生成的配置文件
docker exec yyhis-web cat /usr/share/nginx/html/config.js

# 或通过HTTP访问
curl http://your-server/config.js
```

### 3. 浏览器验证
打开浏览器开发者工具，在Console中应该看到：
```
📋 运行时配置已加载: {VITE_YINGCHUNHUA_SDK_URL: "...", ...}
```

## 配置优先级

1. **运行时配置**（最高优先级）：容器启动时通过环境变量生成
2. **构建时配置**：编译时的环境变量
3. **默认配置**（最低优先级）：代码中的默认值

## 故障排除

### 问题1：配置文件未生成
```bash
# 检查脚本权限
docker exec yyhis-web ls -la /usr/local/bin/generate-runtime-config.sh

# 手动执行脚本
docker exec yyhis-web /usr/local/bin/generate-runtime-config.sh
```

### 问题2：环境变量未传递
```bash
# 检查容器环境变量
docker exec yyhis-web env | grep VITE_
```

### 问题3：前端未读取到配置
- 检查浏览器控制台是否有错误
- 确认`/config.js`文件可以正常访问
- 检查nginx配置中的`/config.js`路由

## 注意事项

1. **安全性**：敏感配置信息会出现在容器环境变量中，请确保容器安全
2. **缓存**：配置文件设置了不缓存头，确保配置更新及时生效
3. **兼容性**：保持与原有构建时配置的兼容性，可以平滑迁移
