FROM nginx:alpine

# 安装bash（用于运行配置生成脚本）
RUN apk add --no-cache bash

# 复制构建产物
COPY dist /usr/share/nginx/html

# 复制配置生成脚本
COPY scripts/generate-runtime-config.sh /usr/local/bin/generate-runtime-config.sh
RUN chmod +x /usr/local/bin/generate-runtime-config.sh

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 创建启动脚本
RUN cat > /usr/local/bin/start.sh << 'EOF'
#!/bin/bash

echo "🚀 启动医院管理系统容器..."

# 生成运行时配置
/usr/local/bin/generate-runtime-config.sh

# 启动nginx
echo "🌐 启动Nginx服务器..."
nginx -g "daemon off;"
EOF

RUN chmod +x /usr/local/bin/start.sh

# 暴露端口
EXPOSE 80

# 使用自定义启动脚本
CMD ["/usr/local/bin/start.sh"]
