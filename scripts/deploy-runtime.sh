#!/bin/bash

# 运行时配置部署脚本
# 用于快速部署支持运行时配置的Docker容器

set -e

echo "🚀 开始部署支持运行时配置的医院管理系统..."

# 配置参数
CONTAINER_NAME="yyhis-web"
IMAGE_NAME="yyhis-web"
PORT="80"
ENV_FILE="env"

# 检查环境变量文件
if [ ! -f "$ENV_FILE" ]; then
    echo "❌ 环境变量文件 '$ENV_FILE' 不存在"
    echo "请创建环境变量文件，包含以下内容："
    echo "VITE_YINGCHUNHUA_SDK_URL=http://183.242.68.188:32103/client_app_iframe/index.js"
    echo "VITE_YINGCHUNHUA_APP_KEY=your_app_key"
    echo "VITE_YINGCHUNHUA_APP_SECRET_KEY=your_secret_key"
    exit 1
fi

echo "📋 使用环境变量文件: $ENV_FILE"
echo "📄 环境变量内容:"
cat "$ENV_FILE"
echo ""

# 停止并删除旧容器
echo "🛑 停止旧容器..."
docker stop "$CONTAINER_NAME" 2>/dev/null || echo "容器 $CONTAINER_NAME 未运行"
docker rm "$CONTAINER_NAME" 2>/dev/null || echo "容器 $CONTAINER_NAME 不存在"

# 构建新镜像
echo "🔨 构建Docker镜像..."
docker build -t "$IMAGE_NAME" .

# 启动新容器
echo "🚀 启动新容器..."
docker run -d \
  --name "$CONTAINER_NAME" \
  -p "$PORT:80" \
  --env-file "$ENV_FILE" \
  --restart unless-stopped \
  "$IMAGE_NAME"

# 等待容器启动
echo "⏳ 等待容器启动..."
sleep 5

# 检查容器状态
if docker ps | grep -q "$CONTAINER_NAME"; then
    echo "✅ 容器启动成功!"
    
    # 显示容器信息
    echo ""
    echo "📊 容器信息:"
    docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    # 显示日志
    echo ""
    echo "📝 容器启动日志:"
    docker logs "$CONTAINER_NAME"
    
    # 验证配置文件
    echo ""
    echo "🔍 验证配置文件:"
    sleep 2
    if docker exec "$CONTAINER_NAME" test -f /usr/share/nginx/html/config.js; then
        echo "✅ 配置文件生成成功"
        echo "📄 配置文件内容:"
        docker exec "$CONTAINER_NAME" cat /usr/share/nginx/html/config.js
    else
        echo "❌ 配置文件未生成"
    fi
    
    echo ""
    echo "🎉 部署完成!"
    echo "🌐 访问地址: http://localhost:$PORT"
    echo "⚙️  配置地址: http://localhost:$PORT/config.js"
    
else
    echo "❌ 容器启动失败"
    echo "📝 错误日志:"
    docker logs "$CONTAINER_NAME"
    exit 1
fi
