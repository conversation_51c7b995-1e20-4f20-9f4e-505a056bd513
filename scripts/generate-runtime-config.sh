#!/bin/bash

# 运行时配置生成脚本
# 用于在Docker容器启动时生成前端运行时配置

echo "🔧 生成运行时配置..."

# 配置文件路径
CONFIG_FILE="/usr/share/nginx/html/config.js"

# 生成配置文件
cat > "$CONFIG_FILE" << EOF
// 运行时配置文件
// 此文件在容器启动时动态生成，优先级高于构建时配置
window.RUNTIME_CONFIG = {
  // 迎春花质控系统配置
  VITE_YINGCHUNHUA_SDK_URL: '${VITE_YINGCHUNHUA_SDK_URL:-}',
  VITE_YINGCHUNHUA_APP_KEY: '${VITE_YINGCHUNHUA_APP_KEY:-}',
  VITE_YINGCHUNHUA_APP_SECRET_KEY: '${VITE_YINGCHUNHUA_APP_SECRET_KEY:-}',
  
  // 其他可能的运行时配置
  VITE_API_BASE_URL: '${VITE_API_BASE_URL:-}',
  VITE_BACKEND_URL: '${VITE_BACKEND_URL:-}',
  
  // 配置生成时间戳
  GENERATED_AT: '$(date -u +"%Y-%m-%dT%H:%M:%SZ")',
  
  // 配置来源标识
  CONFIG_SOURCE: 'runtime'
};

console.log('📋 运行时配置已加载:', window.RUNTIME_CONFIG);
EOF

echo "✅ 运行时配置生成完成: $CONFIG_FILE"

# 显示生成的配置内容（用于调试）
echo "📄 配置内容:"
cat "$CONFIG_FILE"
